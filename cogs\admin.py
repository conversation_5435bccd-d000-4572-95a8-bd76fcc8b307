
from typing import Optional

import discord
from discord import app_commands

from ..util import MinecraftCogBase, admin_only


class AdminCommands(MinecraftCogBase):
    # -------- console execute & audit --------
    @app_commands.command(name="execute", description="Execute a Minecraft command on the server console.")
    @admin_only()
    @app_commands.describe(command_string="The command to run (e.g., 'summon pig 0 10 0')")
    async def execute(self, interaction: discord.Interaction, command_string: str):
        await self.safe_defer(interaction, ephemeral=True)
        def dispatch_command():
            cs = self.server.command_sender
            return self.server.dispatch_command(cs, command_string)
        ok = await self._run_on_server_thread(dispatch_command)
        await self.bot.audit(f"**{interaction.user}** executed: `{command_string}`")
        await interaction.followup.send("✅ Dispatched." if ok else "⚠️ Command not found.", ephemeral=True)

    # -------- moderation: kick / ban / unban --------
    @app_commands.command(name="kick", description="Kick a player (optional reason).")
    @admin_only()
    async def kick(self, interaction: discord.Interaction, player: str, reason: Optional[str] = None):
        await self.safe_defer(interaction, ephemeral=True)
        cmd = f'kick {player} {reason or ""}'.strip()
        ok = await self._run_on_server_thread(lambda: self.server.dispatch_command(self.server.command_sender, cmd))
        await self.bot.audit(f"**{interaction.user}** kicked **{player}** ({reason or 'no reason'})")
        await interaction.followup.send("✅ Kicked." if ok else "⚠️ Failed.", ephemeral=True)

    @app_commands.command(name="ban", description="Ban a player (optional reason).")
    @admin_only()
    async def ban(self, interaction: discord.Interaction, player: str, reason: Optional[str] = None):
        await self.safe_defer(interaction, ephemeral=True)
        def do():
            cs = self.server.command_sender
            return self.server.dispatch_command(cs, f'ban {player} {reason or ""}'.strip()) or \
                   self.server.dispatch_command(cs, f"kick {player} {('[Banned] ' + reason) if reason else '[Banned]'}")
        ok = await self._run_on_server_thread(do)
        await self.bot.audit(f"**{interaction.user}** ban **{player}** ({reason or 'no reason'})")
        await interaction.followup.send("✅ Banned (or kicked as fallback)." if ok else "⚠️ Failed.", ephemeral=True)

    @app_commands.command(name="unban", description="Unban a player.")
    @admin_only()
    async def unban(self, interaction: discord.Interaction, player: str):
        await self.safe_defer(interaction, ephemeral=True)
        def do():
            cs = self.server.command_sender
            return self.server.dispatch_command(cs, f"pardon {player}") or \
                   self.server.dispatch_command(cs, f"unban {player}")
        ok = await self._run_on_server_thread(do)
        await self.bot.audit(f"**{interaction.user}** unban **{player}**")
        await interaction.followup.send("✅ Unbanned." if ok else "⚠️ Failed.", ephemeral=True)

    # -------- allowlist/whitelist --------
    def _allow_or_white(self, subcmd: str) -> bool:
        cs = self.server.command_sender
        return self.server.dispatch_command(cs, f"allowlist {subcmd}") or \
               self.server.dispatch_command(cs, f"whitelist {subcmd}")

    @app_commands.command(name="allowlist_add", description="Add a player to the allowlist/whitelist.")
    @admin_only()
    async def allowlist_add(self, interaction: discord.Interaction, player: str):
        await self.safe_defer(interaction, ephemeral=True)
        ok = await self._run_on_server_thread(lambda: self._allow_or_white(f"add {player}"))
        await self.bot.audit(f"**{interaction.user}** allowlist add **{player}**")
        await interaction.followup.send("✅ Added." if ok else "⚠️ Failed.", ephemeral=True)

    @app_commands.command(name="allowlist_remove", description="Remove a player from the allowlist/whitelist.")
    @admin_only()
    async def allowlist_remove(self, interaction: discord.Interaction, player: str):
        await self.safe_defer(interaction, ephemeral=True)
        ok = await self._run_on_server_thread(lambda: self._allow_or_white(f"remove {player}"))
        await self.bot.audit(f"**{interaction.user}** allowlist remove **{player}**")
        await interaction.followup.send("✅ Removed." if ok else "⚠️ Failed.", ephemeral=True)

    @app_commands.command(name="allowlist_on", description="Enable the allowlist/whitelist.")
    @admin_only()
    async def allowlist_on(self, interaction: discord.Interaction):
        await self.safe_defer(interaction, ephemeral=True)
        ok = await self._run_on_server_thread(lambda: self._allow_or_white("on"))
        await self.bot.audit(f"**{interaction.user}** allowlist ON")
        await interaction.followup.send("✅ Enabled." if ok else "⚠️ Failed.", ephemeral=True)

    @app_commands.command(name="allowlist_off", description="Disable the allowlist/whitelist.")
    @admin_only()
    async def allowlist_off(self, interaction: discord.Interaction):
        await self.safe_defer(interaction, ephemeral=True)
        ok = await self._run_on_server_thread(lambda: self._allow_or_white("off"))
        await self.bot.audit(f"**{interaction.user}** allowlist OFF")
        await interaction.followup.send("✅ Disabled." if ok else "⚠️ Failed.", ephemeral=True)

    @app_commands.command(name="allowlist_list", description="Show allowlist/whitelist (printed in console).")
    @admin_only()
    async def allowlist_list(self, interaction: discord.Interaction):
        await self.safe_defer(interaction, ephemeral=True)
        ok = await self._run_on_server_thread(lambda: self._allow_or_white("list"))
        await self.bot.audit(f"**{interaction.user}** allowlist LIST")
        await interaction.followup.send("🖨️ Printed to server console." if ok else "⚠️ Failed.", ephemeral=True)

    # -------- soft mute --------
    @app_commands.command(name="mute", description="Soft-mute a player (plugin-side).")
    @admin_only()
    async def mute(self, interaction: discord.Interaction, player: str):
        await self.safe_defer(interaction, ephemeral=True)
        def do():
            muted = getattr(self.plugin, "_muted", set())
            self.plugin._muted = muted
            muted.add(player.lower())
            return True
        await self._run_on_server_thread(do)
        await self.bot.audit(f"**{interaction.user}** muted **{player}**")
        await interaction.followup.send(f"🔇 **{player}** muted.", ephemeral=True)

    @app_commands.command(name="unmute", description="Remove soft-mute.")
    @admin_only()
    async def unmute(self, interaction: discord.Interaction, player: str):
        await self.safe_defer(interaction, ephemeral=True)
        def do():
            muted = getattr(self.plugin, "_muted", set())
            if player.lower() in muted:
                muted.remove(player.lower())
                return True
            return False
        ok = await self._run_on_server_thread(do)
        await self.bot.audit(f"**{interaction.user}** unmuted **{player}**")
        await interaction.followup.send(f"🔊 **{player}** unmuted." if ok else "ℹ️ Was not muted.", ephemeral=True)

    # -------- economy: add/remove only (offline-safe) --------
    @app_commands.command(name="pay", description="Modify Money: add/remove only (offline-safe).")
    @admin_only()
    @app_commands.choices(action=[
        app_commands.Choice(name="add", value="add"),
        app_commands.Choice(name="remove", value="remove"),
    ])
    async def pay(self, interaction: discord.Interaction, action: app_commands.Choice[str], player: str, amount: int):
        await self.safe_defer(interaction, ephemeral=True)
        econ_obj = self.plugin.config.economy.scoreboard_objective
        delta = amount if action.value == "add" else -amount

        def op():
            note = f"§aYour {econ_obj} was {'increased' if delta>0 else 'decreased'} by {abs(amount)}."
            return self.plugin.apply_or_queue_score_delta(player, econ_obj, delta, note)

        ok = await self._run_on_server_thread(op)
        await self.bot.audit(f"**{interaction.user}** pay {action.value} `{amount}` for **{player}** on **{econ_obj}**")
        await interaction.followup.send("✅ Queued/applied." if ok else "⚠️ Failed.", ephemeral=True)

    # -------- bounty: stackable & full payout (offline-safe) --------
    @app_commands.command(name="bounty_set", description="Add to a player's bounty; payer funds from Money.")
    @admin_only()
    async def bounty_set(self, interaction: discord.Interaction, target: str, amount: int, payer: Optional[str] = None):
        await self.safe_defer(interaction, ephemeral=False)
        if amount <= 0:
            await interaction.followup.send("Amount must be > 0.", ephemeral=True); return

        bounty_obj = self.plugin.config.bounty.scoreboard_objective
        econ_obj   = self.plugin.config.economy.scoreboard_objective
        who_pays   = payer or self.plugin.config.bounty.server_payer_name

        def do():
            ok1 = self.plugin.apply_or_queue_score_delta(
                target, bounty_obj, +amount, f"§eA bounty increased on you by {amount}."
            )
            ok2 = self.plugin.apply_or_queue_score_delta(
                who_pays, econ_obj, -amount, f"§cYou funded a bounty: -{amount} {econ_obj}."
            )
            if ok1 and ok2:
                self.plugin._bounty_index[target] = self.plugin._bounty_index.get(target, 0) + amount
                self.plugin._bounty_placements[target] = who_pays
                self.plugin._save_bounties_to_config()
                return True
            return False

        ok = await self._run_on_server_thread(do)
        if ok:
            ch = await self.bot._resolve_named_channel("bounty")
            if ch:
                embed = discord.Embed(
                    title="🎯 Bounty Increased",
                    description=f"**{who_pays}** added **{amount}** to **{target}**",
                    color=0xF59E0B,
                )
                total = self.plugin._bounty_index.get(target, amount)
                embed.add_field(name="Target", value=target, inline=True)
                embed.add_field(name="Now Total", value=str(total), inline=True)
                embed.add_field(name="Funded By", value=who_pays, inline=True)
                await ch.send(embed=embed)  # type: ignore
            await self.plugin._update_bounty_board_embed()
            await self.bot.audit(f"**{interaction.user}** bounty_set target={target} +{amount} payer={who_pays}")
            await interaction.followup.send("✅ Bounty increased.")
        else:
            await interaction.followup.send("⚠️ Failed.", ephemeral=True)

    @app_commands.command(
        name="bounty_pay",
        description="Claim a bounty: pays the full current bounty to the claimer."
    )
    @admin_only()
    async def bounty_pay(self, interaction: discord.Interaction, claimer: str, target: str):
        await self.safe_defer(interaction, ephemeral=False)
        bounty_obj = self.plugin.config.bounty.scoreboard_objective
        econ_obj   = self.plugin.config.economy.scoreboard_objective

        def resolve_and_apply():
            current = self.plugin._bounty_index.get(target, 0)
            if not current:
                # fallback to scoreboard value if available
                obj = self.server.scoreboard.get_objective(bounty_obj)
                if obj:
                    sc = obj.get_score(target)
                    val = getattr(sc, "value", None) if sc else None
                    if isinstance(val, int) and val > 0:
                        current = val
            if not current:
                return False, 0

            ok1 = self.plugin.apply_or_queue_score_delta(
                target, bounty_obj, -current, f"§eYour bounty was cleared ({current})."
            )
            ok2 = self.plugin.apply_or_queue_score_delta(
                claimer, econ_obj, +current, f"§aYou claimed a bounty: +{current} {econ_obj}."
            )
            if ok1 and ok2:
                self.plugin._bounty_index.pop(target, None)
                self.plugin._bounty_placements.pop(target, None)
                self.plugin._save_bounties_to_config()
                return True, current
            return False, current

        ok, paid = await self._run_on_server_thread(resolve_and_apply)
        if ok:
            ch = await self.bot._resolve_named_channel("bounty")
            if ch:
                embed = discord.Embed(
                    title="💰 Bounty Claimed",
                    description=f"**{claimer}** claimed **{paid}** for bounty on **{target}**.",
                    color=0xFEE75C,
                )
                embed.add_field(name="Target", value=target, inline=True)
                embed.add_field(name="Claimer", value=claimer, inline=True)
                embed.add_field(name="Paid", value=str(paid), inline=True)
                await ch.send(embed=embed)  # type: ignore
            await self.plugin._update_bounty_board_embed()
            await self.bot.audit(f"**{interaction.user}** bounty_pay target={target} claimer={claimer} paid={paid}")
            await interaction.followup.send("✅ Bounty paid.")
        else:
            await interaction.followup.send("⚠️ No active bounty for that target.", ephemeral=True)

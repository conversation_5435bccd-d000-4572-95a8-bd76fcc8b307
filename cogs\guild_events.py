from __future__ import annotations

import discord
from discord.ext import commands


class GuildEvents(commands.Cog):
    """Posts rich embeds when members join/leave the Discord guild."""

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        # We assume DiscordBot sets self.plugin to the Endstone plugin instance
        self.plugin = getattr(bot, "plugin", None)

    # Helper: restrict to the configured primary guild if set
    def _is_primary_guild(self, guild: discord.Guild | None) -> bool:
        if guild is None:
            return False
        cfg = getattr(self.plugin, "config", None)
        if not cfg:
            return True
        gid = int(getattr(cfg.discord, "guild_id", 0) or 0)
        return gid == 0 or guild.id == gid

    @commands.Cog.listener()
    async def on_member_join(self, member: discord.Member):
        if not self._is_primary_guild(member.guild):
            return

        # Resolve target channel by name "welcome" from [channels]
        ch = await self.bot._resolve_named_channel("welcome")  # provided by DiscordBot
        if not ch:
            return

        # Build nice welcome embed
        embed = discord.Embed(
            title="👋 New Member",
            description=f"{member.mention} joined the server.",
            color=0x3BA55C,  # green
        )
        try:
            embed.set_thumbnail(url=member.display_avatar.url)
        except Exception:
            pass
        embed.add_field(name="User", value=f"{member} (`{member.id}`)", inline=False)
        if member.created_at:
            embed.add_field(
                name="Account Created",
                value=discord.utils.format_dt(member.created_at, style="R"),
                inline=True,
            )
        if member.guild and member.guild.member_count is not None:
            embed.add_field(name="Member Count", value=str(member.guild.member_count), inline=True)
        embed.timestamp = discord.utils.utcnow()

        try:
            await ch.send(embed=embed)  # type: ignore[arg-type]
        except Exception:
            # Never crash the bridge on Discord API hiccups
            pass

    @commands.Cog.listener()
    async def on_member_remove(self, member: discord.Member):
        # Note: member may be a cached Member; if not cached, discord.py still passes a Member-ish object
        guild = member.guild if hasattr(member, "guild") else None
        if not self._is_primary_guild(guild):
            return

        ch = await self.bot._resolve_named_channel("leave")
        if not ch:
            return

        embed = discord.Embed(
            title="🚪 Member Left",
            description=f"**{getattr(member, 'name', 'A user')}** has left the server.",
            color=0xED4245,  # red
        )
        # We may not always have an avatar here, but try
        try:
            embed.set_thumbnail(url=member.display_avatar.url)
        except Exception:
            pass

        tag = f"{getattr(member, 'name', 'unknown')}#{getattr(member, 'discriminator', '0000')}"
        uid = getattr(member, "id", None)
        embed.add_field(name="User", value=f"{tag}" + (f" (`{uid}`)" if uid else ""), inline=False)

        if guild and guild.member_count is not None:
            embed.add_field(name="Member Count", value=str(guild.member_count), inline=True)

        embed.timestamp = discord.utils.utcnow()

        try:
            await ch.send(embed=embed)  # type: ignore[arg-type]
        except Exception:
            pass

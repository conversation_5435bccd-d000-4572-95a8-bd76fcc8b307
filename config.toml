# =============================
# File: endstone_discord_bridge/config.toml (template)
# =============================
# Drop this in plugins/endstone_discord_bridge/config.toml and edit IDs/tokens.

[discord]
# REQUIRED — your bot token
token = "MTE3MzQxMjE4NzQzMjI4ODI3Ng.GyIFuN.U5NRm7dvYegLUJoxTWvSHDVsAeL4tNhYjK7tGs"

# REQUIRED - The primary Guild ID the bot operates in (if using slash commands/guild-specific features)
guild_id = 1337797121486884956

# Your admin role IDs (these roles + Discord admins can use admin-only commands)
admin_role_ids = [1338704692947062794]

# Optional: dev guild to sync commands instantly
dev_guild_id = 1337797121486884956

# Optional: webhook mode (requires webhook_url if features.webhook_mode is true)
# webhook_url = ""
webhook_avatar_template = "https://minotar.net/avatar/{name}.png"

[features]
# Mirror edits/deletes from Discord into MC
relay_edits = false
relay_deletes = false

# Event features toggles
command_spy = false
advancements = false

# Change Discord nickname to MC name after linking
nick_sync = true

# Use webhook mode for MC→Discord
webhook_mode = true

# Global toggle for /list delivery (true -> DM, false -> channel)
list_dm_default = true

[presence]
# What the bot is doing: playing | listening | watching | competing | custom
type = "playing"
# Template supports {online} → replaced with online player count
template = "Enhancing The Kingdom with Onistone By TheN1NJ4LL0 with {players} Players! | TPS {tps:.2f}"

[announcements]
# Post a daily announcement at the given time (server local time)
enabled = false
time_h = 12
time_m = 0
message = "High-noon event starts now!"

[channels]
# Define the actual channel IDs here
relay = 1337802315784257587          # Default Discord↔Minecraft chat bridge
global = 1337802315784257587         # Default for global events
staff = 1337800655481602159          # For admin/audit logs and staff chat
heartbeat = 1337802315784257587      # For hourly server status updates
trade = 1344154390835101793
audit = 1337800655481602159
bounty_updates = 1413557556525142046
bounty_board = 1411404578313732307
welcome = 0   # Channel for Discord join embeds
leave   = 0   # Channel for Discord leave embeds

[routing]
# Route events to named channels from [channels]
join = "global"
quit = "global"
death = "global"
command_spy = "staff"
advancements = "global"
announcements = "relay"
audit_log = "audit" # For logging /execute usage

# Prefix-based routing for MC chat
chat_staff_prefix = "!staff"
chat_trade_prefix = "!trade"

[linking]
# Lifetime for codes generated by /link (in minutes)
code_ttl_minutes = 10


[economy]
# The scoreboard objective for currency
scoreboard_objective = "Money"

[bounty]
# The scoreboard objective for active bounties
scoreboard_objective = "Money"
server_payer_name = "The Kingdom"
placements = {}
index = {}
board_message_id = 0
bump_on_change = true

[lang]
# Localized templates for MC→Discord embeds
join  = "➡️ {name} joined the server."
quit  = "⬅️ {name} left the server."
death = "{message}"

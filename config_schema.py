
import tomllib, json
from dataclasses import dataclass, field, asdict
from pathlib import Path
from typing import Any


@dataclass
class DiscordSection:
    token: str = ""
    guild_id: int = 0
    admin_role_ids: list[int] = field(default_factory=lambda: [0])
    dev_guild_id: int = 0
    webhook_url: str = ""
    webhook_avatar_template: str = "https://minotar.net/avatar/{name}.png"


@dataclass
class FeaturesSection:
    relay_edits: bool = True
    relay_deletes: bool = True
    command_spy: bool = False
    advancements: bool = False
    nick_sync: bool = True
    webhook_mode: bool = False
    list_dm_default: bool = True


@dataclass
class PresenceSection:
    type: str = "playing"  # playing|listening|watching|competing|custom
    template: str = "Playing with {online} players"


@dataclass
class AnnouncementsSection:
    enabled: bool = False
    time_h: int = 12
    time_m: int = 0
    message: str = "High-noon event starts now!"


@dataclass
class ChannelsSection:
    relay: int = 0
    global_: int = 0  # stored as global_ in Python, written as 'global' in TOML
    staff: int = 0
    audit: int = 0                 # NEW: dedicated audit channel
    bounty_updates: int = 0        # NEW: per-bounty event posts (placements/claims)
    bounty_board: int = 0          # NEW: evergreen bounty board message lives here
    heartbeat: int = 0
    trade: int = 0
    welcome: int = 0      # NEW
    leave: int = 0        # NEW

    def to_names(self) -> dict[str, int]:
        return {
            "relay": self.relay,
            "global": self.global_,
            "staff": self.staff,
            "audit": self.audit,
            "bounty_updates": self.bounty_updates,
            "bounty_board": self.bounty_board,
            "heartbeat": self.heartbeat,
            "trade": self.trade,
            "welcome": self.welcome,  # NEW
            "leave": self.leave,  # NEW
        }


@dataclass
class RoutingSection:
    join: str = "global"
    quit: str = "global"
    death: str = "global"
    command_spy: str = "staff"
    advancements: str = "global"
    announcements: str = "relay"
    audit_log: str = "audit"   # default now points to dedicated audit channel
    chat_staff_prefix: str = "[STAFF]"
    chat_trade_prefix: str = "[trade]"


@dataclass
class LinkingSection:
    # TTL only; pending/links kept for back-compat and optional in-file use
    code_ttl_minutes: int = 10
    pending: dict[str, dict[str, Any]] = field(default_factory=dict)  # uuid -> {code, expires, name}
    links: dict[str, str] = field(default_factory=dict)               # uuid -> discord_id


@dataclass
class EconomySection:
    scoreboard_objective: str = "Money"


@dataclass
class BountySection:
    scoreboard_objective: str = "Bounties"
    server_payer_name: str = "The Kingdom"
    # These mirrors are no longer used for persistence (migrated to bounties.toml),
    # but we keep fields for back-compat and to hold runtime defaults if needed.
    placements: dict[str, str] = field(default_factory=dict)
    index: dict[str, int] = field(default_factory=dict)
    board_message_id: int = 0
    bump_on_change: bool = False   # whether to re-post board to bottom on changes


@dataclass
class LangSection:
    join: str = "➡️ {name} joined the server."
    quit: str = "⬅️ {name} left the server."
    death: str = "{message}"


@dataclass
class BridgeConfig:
    discord: DiscordSection = field(default_factory=DiscordSection)
    features: FeaturesSection = field(default_factory=FeaturesSection)
    presence: PresenceSection = field(default_factory=PresenceSection)
    announcements: AnnouncementsSection = field(default_factory=AnnouncementsSection)
    channels: ChannelsSection = field(default_factory=ChannelsSection)
    routing: RoutingSection = field(default_factory=RoutingSection)
    linking: LinkingSection = field(default_factory=LinkingSection)
    economy: EconomySection = field(default_factory=EconomySection)
    bounty: BountySection = field(default_factory=BountySection)
    lang: LangSection = field(default_factory=LangSection)

    @staticmethod
    def load(path: Path) -> "BridgeConfig":
        if not path.exists():
            cfg = BridgeConfig()
            cfg.save(path)
            return cfg

        raw = path.read_text(encoding="utf-8")
        data = tomllib.loads(raw)

        def as_section(section_cls, key):
            src = dict(data.get(key, {}))
            # map TOML 'channels.global' -> dataclass 'global_'
            if key == "channels" and "global" in src and "global_" not in src:
                src["global_"] = src.pop("global")
            return section_cls(**{**asdict(section_cls()), **src})

        cfg = BridgeConfig(
            discord=as_section(DiscordSection, "discord"),
            features=as_section(FeaturesSection, "features"),
            presence=as_section(PresenceSection, "presence"),
            announcements=as_section(AnnouncementsSection, "announcements"),
            channels=as_section(ChannelsSection, "channels"),
            routing=as_section(RoutingSection, "routing"),
            linking=as_section(LinkingSection, "linking"),
            economy=as_section(EconomySection, "economy"),
            bounty=as_section(BountySection, "bounty"),
            lang=as_section(LangSection, "lang"),
        )
        return cfg

    def save(self, path: Path):
        # Minimal TOML writer (no external deps)
        def _quote(s: str) -> str:
            return json.dumps(s)

        def _val(v) -> str:
            if isinstance(v, bool):
                return "true" if v else "false"
            if isinstance(v, int):
                return str(v)
            if isinstance(v, float):
                return ("%f" % v).rstrip("0").rstrip(".")
            if isinstance(v, str):
                return _quote(v)
            if isinstance(v, list):
                return "[" + ", ".join(_val(x) for x in v) + "]"
            if isinstance(v, dict):
                items = []
                for k, vv in v.items():
                    items.append(f"{k} = {_val(vv)}")
                return "{" + ", ".join(items) + "}"
            return _quote(str(v))

        data = asdict(self)
        # map dataclass 'channels.global_' -> TOML 'channels.global'
        if "channels" in data and isinstance(data["channels"], dict):
            ch = data["channels"]
            if "global_" in ch:
                ch["global"] = ch.pop("global_")

        lines: list[str] = []
        for section, content in data.items():
            if not isinstance(content, dict):
                continue
            lines.append(f"[{section}]")
            for k, v in content.items():
                lines.append(f"{k} = {_val(v)}")
            lines.append("")
        out = "\n".join(lines).rstrip() + "\n"

        tmp = path.with_suffix(".tmp")
        tmp.write_text(out, encoding="utf-8")
        tmp.replace(path)

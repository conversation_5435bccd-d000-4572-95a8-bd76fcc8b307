
import asyncio
import string
import threading
from datetime import datetime, timedelta, timezone
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, List, Tuple as Tup

import discord  # embeds

from endstone.plugin import Plugin
from endstone.command import Command, CommandSender
from endstone.event import (
    event_handler,
    PlayerChatEvent,
    PlayerJoinEvent,
    PlayerQuitEvent,
    PlayerDeathEvent,
    PlayerCommandEvent,
)
from endstone import Player

from .config_schema import BridgeConfig
from .linked_store import LinkedStore
from .bounty_store import BountyStore
from .bot import DiscordBot
from .util import SyncRunner, translate_component


class DiscordBridgePlugin(Plugin):
    api_version = "0.10"

    @property
    def config(self) -> BridgeConfig:  # type: ignore[override]
        return self._cfg

    commands = {
        "link": {
            "description": "Generate a code to link your Discord account (/verify in Discord).",
            "usages": ["/link"],
            "permissions": ["endstone_discord_bridge.link"],
        }
    }

    permissions = {
        "endstone_discord_bridge.link": {
            "description": "Allow players to use /link to link Discord.",
            "default": True,
        }
    }

    # ---------- lifecycle ----------
    def on_load(self) -> None:
        self.data_folder.mkdir(parents=True, exist_ok=True)

    def on_enable(self) -> None:
        cfg_path = self.data_folder / "config.toml"
        self._cfg = BridgeConfig.load(cfg_path)

        # Linked store
        self.linked_path = self.data_folder / "linked.toml"
        self.linked: LinkedStore = LinkedStore.load(self.linked_path)

        migrated_links = False
        link_cfg = getattr(self._cfg, "linking", None)
        if link_cfg is not None:
            cfg_pending = getattr(link_cfg, "pending", None)
            cfg_links = getattr(link_cfg, "links", None)
            if isinstance(cfg_pending, dict) and cfg_pending:
                self.linked.pending.update(cfg_pending)
                try: cfg_pending.clear()
                except Exception: pass
                migrated_links = True
            if isinstance(cfg_links, dict) and cfg_links:
                self.linked.links.update(cfg_links)
                try: cfg_links.clear()
                except Exception: pass
                migrated_links = True
        if migrated_links:
            self.save_linked()
            try: self._cfg.save(cfg_path)
            except Exception: pass

        # Bounty store (external file) + one-time migration out of config
        self.bounties_path = self.data_folder / "bounties.toml"
        self.bounties: BountyStore = BountyStore.load(self.bounties_path)

        migrated_bounties = False
        try:
            old_idx = dict(getattr(self._cfg.bounty, "index", {}) or {})
            old_plc = dict(getattr(self._cfg.bounty, "placements", {}) or {})
            old_mid = int(getattr(self._cfg.bounty, "board_message_id", 0) or 0)
            if old_idx and not self.bounties.index:
                self.bounties.index.update(old_idx); migrated_bounties = True
            if old_plc and not self.bounties.placements:
                self.bounties.placements.update(old_plc); migrated_bounties = True
            if old_mid and not self.bounties.board_message_id:
                self.bounties.board_message_id = old_mid; migrated_bounties = True
            if migrated_bounties:
                self.save_bounties()
                # Clear mirrors in config to avoid reload issues
                try:
                    self._cfg.bounty.index = {}
                    self._cfg.bounty.placements = {}
                    self._cfg.bounty.board_message_id = 0
                    self._cfg.save(cfg_path)
                except Exception:
                    pass
        except Exception:
            pass

        # soft-mute store
        self._muted: set[str] = set()

        # offline-safe scoreboard queue: name_lower -> list[(objective, delta, notify)]
        self._score_ops: Dict[str, List[Tup[str, int, str]]] = {}

        self.sync = SyncRunner(self.server, self)
        self.loop: Optional[asyncio.AbstractEventLoop] = None
        self.bot_thread: Optional[threading.Thread] = None
        self.bot: Optional[DiscordBot] = None

        self.start_bot_thread()
        self.register_events(self)
        self.logger.info("Discord bridge enabled.")
        self._relay("🟢 **Server started**")

        # Defer bounty board refresh until the bot + loop are definitely ready
        self._schedule_bounty_board_refresh()

    def on_disable(self) -> None:
        self.logger.info("Shutting down Discord bridge…")
        self._relay("🔴 **Server stopping**")
        try:
            if self.bot and self.bot.loop and self.bot.loop.is_running():
                fut = asyncio.run_coroutine_threadsafe(self.bot.close(), self.bot.loop)
                try: fut.result(timeout=10)
                except Exception: pass
            if self.loop and self.loop.is_running():
                self.loop.call_soon_threadsafe(self.loop.stop)
            if self.bot_thread and self.bot_thread.is_alive():
                self.bot_thread.join(timeout=5)
        except Exception:
            pass
        self.bot = None

    # ---------- threading / bot ----------
    def start_bot_thread(self) -> None:
        if self.bot_thread and self.bot_thread.is_alive():
            return
        if not self.config.discord.token:
            self.logger.error("No Discord token configured. Set [discord].token in config.toml.")
            return

        def run():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            self.loop = loop
            bot = DiscordBot(self, self.config)
            self.bot = bot
            loop.create_task(bot.start(self.config.discord.token))
            loop.run_forever()

        self.bot_thread = threading.Thread(target=run, name="DiscordBotThread", daemon=True)
        self.bot_thread.start()

    # Defer + retry scheduler for the bounty board refresh until bot.loop exists
    def _schedule_bounty_board_refresh(self, delay: float = 0.5) -> None:
        def try_schedule():
            bot = self.bot
            if not bot or not getattr(bot, "loop", None) or bot.loop.is_closed():
                threading.Timer(delay, try_schedule).start()
                return

            def _schedule_inside_loop():
                async def _go():
                    try:
                        await bot.wait_until_ready()
                        await self._update_bounty_board_embed()
                    except Exception:
                        self.logger.exception("Initial bounty board refresh failed")
                asyncio.create_task(_go())

            bot.loop.call_soon_threadsafe(_schedule_inside_loop)

        try_schedule()

    # ---------- config & store I/O ----------
    def save_linked(self) -> None:
        try:
            self.linked.save(self.linked_path)
        except Exception as e:
            self.logger.error(f"Failed saving linked store: {e}")

    def save_bounties(self) -> None:
        try:
            self.bounties.save()
        except Exception as e:
            self.logger.error(f"Failed saving bounty store: {e}")

    def _relay(self, text: str) -> None:
        try:
            if not (self.bot and self.bot.loop and not self.bot.loop.is_closed()):
                return
            async def _coro():
                try:
                    await self.bot.wait_until_ready()
                    ch = await self.bot._resolve_named_channel("relay")
                    if ch:
                        await ch.send(text)  # type: ignore
                except Exception:
                    pass
            asyncio.run_coroutine_threadsafe(_coro(), self.bot.loop)
        except Exception:
            pass

    # ---------- helpers ----------
    def _generate_link_code(self) -> str:
        alphabet = string.ascii_uppercase + string.digits
        import random
        return "".join(random.choice(alphabet) for _ in range(6))

    def lookup_name_by_uuid(self, uuid: str) -> Optional[str]:
        try:
            p = self.server.get_player(uuid)
            return p.name if p else None
        except Exception:
            return None

    def _world_and_coords(self, player) -> Tuple[Optional[str], Optional[str]]:
        try:
            lvl = getattr(player, "level", None) or getattr(getattr(player, "location", None), "level", None)
            world = getattr(lvl, "name", None) or getattr(lvl, "level_name", None)
        except Exception:
            world = None
        try:
            loc = getattr(player, "location", None)
            x = int(round(getattr(loc, "x", 0)))
            y = int(round(getattr(loc, "y", 0)))
            z = int(round(getattr(loc, "z", 0)))
            coords = f"{x}, {y}, {z}"
        except Exception:
            coords = None
        return world, coords

    def _minotar(self, name: str) -> str:
        return (self.config.discord.webhook_avatar_template or "").format(name=name)

    def _online_player_obj(self, name: str) -> Optional[Player]:
        n = name.lower()
        for p in self.server.online_players:
            if p.name.lower() == n:
                return p
        return None

    # ---- offline-safe scoreboard mutator (main thread only) ----
    def apply_or_queue_score_delta(self, player_name: str, objective: str, delta: int, notify_msg: str) -> bool:
        player = self._online_player_obj(player_name)
        cs = self.server.command_sender
        if player is not None:
            ok = True
            if delta > 0:
                ok = self.server.dispatch_command(cs, f"scoreboard players add {player_name} {objective} {delta}")
            elif delta < 0:
                ok = self.server.dispatch_command(cs, f"scoreboard players remove {player_name} {objective} {abs(delta)}")
            if ok:
                try:
                    player.send_message(notify_msg)
                except Exception:
                    pass
            return ok
        key = player_name.lower()
        self._score_ops.setdefault(key, []).append((objective, delta, notify_msg))
        return True

    def _flush_score_ops_for(self, player: Player):
        key = player.name.lower()
        ops = self._score_ops.pop(key, [])
        if not ops:
            return
        cs = self.server.command_sender
        for objective, delta, notify in ops:
            ok = True
            if delta > 0:
                ok = self.server.dispatch_command(cs, f"scoreboard players add {player.name} {objective} {delta}")
            elif delta < 0:
                ok = self.server.dispatch_command(cs, f"scoreboard players remove {player.name} {objective} {abs(delta)}")
            if ok:
                try:
                    player.send_message(notify)
                except Exception:
                    pass

    # --- Bounty embeds ---

    async def _post_bounty_update(self, embed: discord.Embed) -> None:
        """Send a single bounty placement/claim embed to the bounty_updates channel."""
        ch = await self.bot._resolve_named_channel("bounty_updates")
        if ch:
            try:
                await ch.send(embed=embed)  # type: ignore
            except Exception:
                pass

    # Evergreen bounty board — posted in channels.bounty_board; supports optional "bump to bottom"
    async def _update_bounty_board_embed(self, force_bump: Optional[bool] = None):
        entries = [(t, a) for t, a in self.bounties.index.items() if a > 0]
        entries.sort(key=lambda x: x[1], reverse=True)
        if entries:
            lines = [
                f"**{t}** — `{a}`"
                + (f"  _(by {self.bounties.placements.get(t)})_" if self.bounties.placements.get(t) else "")
                for t, a in entries
            ]
            desc = "\n".join(lines[:50])
            color = 0xF59E0B
        else:
            desc = "_No active bounties_"
            color = 0x2B2D31

        embed = discord.Embed(title="Bounty Board", description=desc, color=color)
        embed.set_footer(text="Auto-updates when bounties change")

        # <-- board lives in the dedicated bounty_board channel
        ch = await self.bot._resolve_named_channel("bounty_board")
        if not ch:
            return

        bump = getattr(self._cfg.bounty, "bump_on_change", False) if force_bump is None else force_bump
        current_id = int(self.bounties.board_message_id or 0)

        if bump:
            new_msg = await ch.send(embed=embed)  # type: ignore
            if current_id:
                try:
                    old = await ch.fetch_message(current_id)  # type: ignore
                    await old.delete()
                except Exception:
                    pass
            self.bounties.board_message_id = int(new_msg.id)
            self.save_bounties()
            return

        if current_id:
            try:
                msg = await ch.fetch_message(current_id)  # type: ignore
                await msg.edit(embed=embed)
                return
            except Exception:
                pass

        m = await ch.send(embed=embed)  # type: ignore
        self.bounties.board_message_id = int(m.id)
        self.save_bounties()

    # ---------- /link ----------
    def on_command(self, sender: CommandSender, command: Command, args: list[str]) -> bool:
        if command.name != "link":
            return False
        if isinstance(sender, Player):
            code = self._generate_link_code()
            uuid = str(sender.unique_id)
            now = datetime.now(timezone.utc)
            exp = now + timedelta(minutes=self.config.linking.code_ttl_minutes)
            self.linked.pending[uuid] = {"code": code, "expires": exp.timestamp(), "name": sender.name}
            self.save_linked()
            sender.send_message(
                f"§aDiscord Link Code: §e{code}§r — Use in Discord: /verify {code} "
                f"(expires in {self.config.linking.code_ttl_minutes}m)"
            )
        else:
            sender.send_message("Run this command in-game.")
        return True

    # ---------- Events (MC → Discord embeds) ----------
    @event_handler
    def on_player_chat(self, event: PlayerChatEvent):
        if getattr(self, "_muted", None) and event.player and event.player.name.lower() in self._muted:
            try:
                event.cancel()
            except Exception:
                try:
                    event.is_cancelled = True
                except Exception:
                    pass
            try:
                event.player.send_message(" §7You are currently muted.§r")
            except Exception:
                pass
            return
        if not self.bot or not self.bot.loop or self.bot.loop.is_closed():
            return

        msg = translate_component(self.server, event.message)
        name = event.player.name

        route = "relay"
        pref_staff = self.config.routing.chat_staff_prefix
        pref_trade = self.config.routing.chat_trade_prefix
        if pref_staff and msg.startswith(pref_staff):
            route = "staff"; msg = msg[len(pref_staff):].lstrip()
        elif pref_trade and msg.startswith(pref_trade):
            route = "trade"; msg = msg[len(pref_trade):].lstrip()

        async def send():
            avatar = self._minotar(name)
            description = f"**{name}**\n{msg}"
            embed = discord.Embed(description=description, color=0x57F287)
            embed.set_author(name=name, icon_url=avatar)
            if self.config.features.webhook_mode and self.config.discord.webhook_url:
                await self.bot.send_via_webhook(username=name, text=None, avatar_url=avatar, embed=embed)  # type: ignore
            else:
                ch = await self.bot._resolve_named_channel(route)
                if ch:
                    await ch.send(embed=embed)  # type: ignore
        asyncio.run_coroutine_threadsafe(send(), self.bot.loop)

    @event_handler
    def on_player_join(self, event: PlayerJoinEvent):
        if not self.bot or not self.bot.loop or self.bot.loop.is_closed():
            return
        name = event.player.name
        text = self.config.lang.join.format(name=name)
        world, coords = self._world_and_coords(event.player)

        async def send():
            # Rich to routed join channel
            ch_rich = await self.bot.route_named("join")
            avatar = self._minotar(name)
            embed = discord.Embed(description=text, color=0x3BA55C)
            embed.set_author(name=name, icon_url=avatar)
            if world:  embed.add_field(name="🌍 World",  value=f"`{world}`", inline=True)
            if coords: embed.add_field(name="📍 Coords", value=f"`{coords}`", inline=True)
            embed.timestamp = discord.utils.utcnow()
            if ch_rich: await ch_rich.send(embed=embed)  # type: ignore

            # Minimal to relay
            ch_relay = await self.bot._resolve_named_channel("relay")
            if ch_relay:
                embed_min = discord.Embed(description=text, color=0x3BA55C)
                embed_min.set_author(name=name, icon_url=avatar)
                embed_min.timestamp = discord.utils.utcnow()
                await ch_relay.send(embed=embed_min)  # type: ignore

        asyncio.run_coroutine_threadsafe(send(), self.bot.loop)
        self._flush_score_ops_for(event.player)

    @event_handler
    def on_player_quit(self, event: PlayerQuitEvent):
        if not self.bot or not self.bot.loop or self.bot.loop.is_closed():
            return
        name = event.player.name
        text = self.config.lang.quit.format(name=name)
        world, coords = self._world_and_coords(event.player)

        async def send():
            ch_rich = await self.bot.route_named("quit")
            avatar = self._minotar(name)
            embed = discord.Embed(description=text, color=0xF59E0B)
            embed.set_author(name=name, icon_url=avatar)
            if world:  embed.add_field(name="🌍 World",  value=f"`{world}`", inline=True)
            if coords: embed.add_field(name="📍 Coords", value=f"`{coords}`", inline=True)
            embed.timestamp = discord.utils.utcnow()
            if ch_rich: await ch_rich.send(embed=embed)  # type: ignore

            ch_relay = await self.bot._resolve_named_channel("relay")
            if ch_relay:
                embed_min = discord.Embed(description=text, color=0xF59E0B)
                embed_min.set_author(name=name, icon_url=avatar)
                embed_min.timestamp = discord.utils.utcnow()
                await ch_relay.send(embed=embed_min)  # type: ignore

        asyncio.run_coroutine_threadsafe(send(), self.bot.loop)

    @event_handler
    def on_player_death(self, event: PlayerDeathEvent):
        if not self.bot or not self.bot.loop or self.bot.loop.is_closed():
            return
        localized = translate_component(self.server, event.death_message)
        text = self.config.lang.death.format(message=localized)
        name = getattr(getattr(event, "player", None), "name", "Player")
        try:
            world, coords = self._world_and_coords(event.player)  # type: ignore
        except Exception:
            world, coords = None, None

        async def send():
            ch = await self.bot.route_named("death")
            if not ch: return
            avatar = self._minotar(name)
            embed = discord.Embed(description=text, color=0xED4245)
            embed.set_author(name=name, icon_url=avatar)
            if world:  embed.add_field(name="🌍 World",  value=f"`{world}`", inline=True)
            if coords: embed.add_field(name="📍 Coords", value=f"`{coords}`", inline=True)
            embed.timestamp = discord.utils.utcnow()
            await ch.send(embed=embed)  # type: ignore

        asyncio.run_coroutine_threadsafe(send(), self.bot.loop)

    @event_handler
    def on_player_command(self, event: PlayerCommandEvent):
        if not self.config.features.command_spy:
            return
        if not self.bot or not self.bot.loop or self.bot.loop.is_closed():
            return

        async def send():
            ch = await self.bot.route_named("command_spy")
            if ch:
                await ch.send(f"🕵️ {event.player.name}: `{event.command}`")  # type: ignore

        asyncio.run_coroutine_threadsafe(send(), self.bot.loop)
